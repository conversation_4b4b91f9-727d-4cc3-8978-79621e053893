# NeoXHelper

A CLI tools which dealing with some requirements on Windows | Web | NeoX production.

# Setup

```bash
cd testing\Tools\NeoXHelper

# Setup python environment with uv
# If you haven't installed uv, please refer to: https://github.com/astral-sh/uv
uv venv
# Activate the virtual environment
# On Windows PowerShell: .\.venv\Scripts\Activate.ps1
# On Linux/macOS: source .venv/bin/activate

# Install dependencies
uv pip install -e .[dev]

# The following command is used to build an executable binary
# Pls configuration the neox.spec before running the following command
pyinstaller neox.spec

# Besides that you can use the following command to build
# The neox.spec config is the same as following command (Pls update {your_env_path} !!!)
pyinstaller --clean --onedir --console --add-data ".\\templates\\template_config.toml:.\\templates" --add-data "{your_env_path}\\Lib\\site-packages\\pyecharts:.\\pyecharts" --add-binary ".\\bin\\SetDpi.exe:.\\bin" --hidden-import common --hidden-import scope neox.py
```

# Usage

Examples:

```context
example 1:
    neox
example 2:
    neox -- help

-> explanation:
    Notice: The format is '-- help' !!!
    example 1 == example 2.

example 3:
    neox template generate --path=.
example 4:
    neox demo generate --path=~/neox

-> explanation:
    Generate config templates to the specified directory.
    template file name:
        1. template_config.toml
    Notice:
        1. Both absolute paths and relative paths can be executed with '--path';
        2. The path must be present when you use '--path'.

example 5:
    neox nsips get --file=config.toml

-> explanation:
    Generate charts and xlsx according to the settings in the configuration file.
    The result files will be generated into src_dir/result.

example 6:
    neox win feed --file=./config.toml
example 7:
    neox windows install --file=config.toml
example 8:
    neox win uninstall --file=~/neox/config.toml
example 9:
    neox win get dpi == neox win get 0
example 10:
    neox windows get resolution == neox win get 1
example 11:
    neox windows set --file=config.toml
example 12:
    neox win mkdirs --file=config.toml

-> explanation:
    Both absolute paths and relative paths can be executed with '--file' when you need to customize config file.
```
