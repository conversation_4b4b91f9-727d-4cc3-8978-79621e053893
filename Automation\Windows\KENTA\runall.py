# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   runall.py
@Software   :   PyCharm
"""

import pytest

if __name__ == "__main__":
    # pytest.main()

    # pytest.main(['-vs', './testsuite/登录登出'])
    # pytest.main(['-vs', './testsuite/登录登出', '--reruns=2', '--reruns-delay=3', '--maxfail=1'])

    # pytest.main(["-vs", "./testsuite/登录登出/test_login.py"])
    # pytest.main(['-vs', './testsuite/登录登出/test_login.py::test_login_fail'])
    pytest.main(["-vs", "./testsuite/登录登出/test_login.py::test_login_success"])

    # pytest.main(['-vs', './testsuite/登录登出/test_logout.py'])
    # pytest.main(['-vs', './testsuite/登录登出/test_logout.py::test_logout'])

    # os.system("allure generate ./temp -o ./report --clean")
    # os.system("allure open ./report")
