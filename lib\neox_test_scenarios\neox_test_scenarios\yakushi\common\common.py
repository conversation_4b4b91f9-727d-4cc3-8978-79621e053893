# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-19
<AUTHOR>   <PERSON><PERSON><PERSON>u
@Email      :   <EMAIL>
@File       :   common.py
@Software   :   PyCharm
"""

from jmespath import search
from neox_test_common import UIA, logger
from uiautomation import WindowControl


def com_show_desktop() -> None:
    """
    This function is used to show the desktop

    Returns:
        None
    """
    UIA.showDesktop()
    logger.info("< Step Common :: 显示桌面 >")


def com_open_yakushi_app(config: dict) -> WindowControl | None:
    """
    This function is used to check if the Yakushi client is running and, if not, to start it.

    Parameters:
        config (dict): A dictionary of test configuration parameters.

    Returns:
        WindowControl or None: A WindowControl object representing the Yakushi client's login window, or None if the client is not running.

    """
    login_window = UIA.WindowControl(
        Name="薬師丸賢太", ClassName="Window", searchDepth=1, timeout=3
    )
    # Check if the Yakushi client is running
    if login_window is None:
        # The client is not running, so start it
        logger.info("< Step Common :: Yakushi客户端未启动 >")
        exe_abs_path = search("yakushi.locate.exe_abs_path", config)
        if UIA.openApplication(app=exe_abs_path):
            # The client started successfully
            logger.info("< Step Common :: 打开Yakushi客户端 >")
            check_cert_installed_timeout = search(
                "yakushi.modules.login.check_cert_installed_timeout", config
            )
            login_window = UIA.WindowControl(
                Name="薬師丸賢太",
                ClassName="Window",
                searchDepth=1,
                timeout=check_cert_installed_timeout,
            )
            # Print information about the client's process and window
            logger.info(f"Yakushi客户端进程号：[ {login_window.ProcessId} ]")
            logger.info(
                f"Yakushi客户端登录窗口-RECT：[ {login_window.BoundingRectangle} ]"
            )
    else:
        # The client is running
        logger.info("< Step Common :: Yakushi客户端已启动 >")

    return login_window


def com_write_acc_info(data: dict) -> bool:
    """
    This function is used to input account information into the Yakushi client.

    Parameters:
        data (dict): A dictionary containing the following keys:
            control (dict): A dictionary containing the following keys:
                class_name (str): The class name of the edit control.
                auto_id (str): The automation ID of the edit control.
            text (str): The text to be entered into the edit control.
            log (dict): A dictionary containing the following keys:
                info (str): The information to be logged when the function completes successfully.
                debug (str): The debug information to be logged when the function completes successfully.

    Returns:
        bool: A boolean value indicating whether the account information was successfully entered.

    """
    control, text, log = data["control"], data["text"], data["log"]

    login_window = UIA.WindowControl(
        Name="薬師丸賢太", ClassName="Window", searchDepth=1
    )
    status_input = False
    if login_window is not None:
        UIA.setWindowActive(login_window)
        text_box = UIA.EditControl(
            parent=login_window,
            ClassName=control["class_name"],
            AutomationId=control["auto_id"],
        )
        _ = UIA.inputEditControlText(editControl=text_box, text="")
        status_input = UIA.inputEditControlText(editControl=text_box, text=text)
        logger.info(f"{log['info']}")
        logger.info(f"{log['debug']}-RECT：[ {text_box.BoundingRectangle} ]")

    return status_input


def com_click_login_btn(data: dict) -> bool:
    """
    This function is used to click the login button in the Yakushi client.

    Parameters:
        data (dict): A dictionary containing the following keys:
            control (dict): A dictionary containing the following keys:
                btn_name (str): The name of the login button.
            log (dict): A dictionary containing the following keys:
                info (str): The information to be logged when the function completes successfully.
                debug (str): The debug information to be logged when the function completes successfully.

    Returns:
        bool: A boolean value indicating whether the login button was successfully clicked.

    """
    control, log = data["control"], data["log"]

    login_window = UIA.WindowControl(
        Name="薬師丸賢太", ClassName="Window", searchDepth=1
    )
    status_click = False
    if login_window is not None:
        UIA.setWindowActive(login_window)
        login_button = UIA.ButtonControl(parent=login_window, Name=control["btn_name"])
        status_click = UIA.clickButton(button=login_button)
        logger.info(f"{log['info']}")
        logger.info(f"{log['debug']}-RECT：[ {login_button.BoundingRectangle} ]")

    return status_click


def com_get_yakushi_window(
    auto_id: str, searchDepth: int, timeout: int = 5
) -> WindowControl | None:
    """
    This function is used to open the Yakushi client's window.
    """
    return UIA.WindowControl(
        Name="薬師丸賢太",
        ClassName="Window",
        AutomationId=auto_id,
        searchDepth=searchDepth,
        timeout=timeout,
    )


def com_open_homepage_from_float_window(data: dict) -> bool:
    """
    This function is used to open the homepage from the Yakushi client's float window.

    Parameters:
        data (dict): A dictionary containing the following keys:
            float_window (dict): A dictionary containing the following keys:
                auto_id (str): The automation ID of the float window.
            main_window (dict): A dictionary containing the following keys:
                auto_id (str): The automation ID of the main window.
            main_window_button (dict): A dictionary containing the following keys:
                auto_id (str): The automation ID of the button that shows the main window.
            float_logo_button (dict): A dictionary containing the following keys:
                auto_id (str): The automation ID of the button that closes the float window.

    Returns:
        bool: A boolean value indicating whether the homepage was successfully opened.

    """
    fw, mw, mwb, flb = (
        data["float_window"],
        data["main_window"],
        data["main_window_button"],
        data["float_logo_button"],
    )
    float_window = com_get_yakushi_window(
        auto_id=fw["auto_id"], searchDepth=1, timeout=3
    )
    main_window = com_get_yakushi_window(
        auto_id=mw["auto_id"], searchDepth=1, timeout=3
    )
    logger.info("< Step Common :: 查找浮动窗口与主页窗口 >")
    logger.info(f"浮动窗口-RECT：[ {float_window.BoundingRectangle} ]")

    if main_window is None:
        # The main window is not open, so try to show it
        logger.info("< Step Common :: 未找到主页窗口 >")
        show_main_window_button = UIA.ButtonControl(
            parent=float_window, Name="", AutomationId=mwb["auto_id"]
        )
        _ = UIA.clickButton(button=show_main_window_button, waitTime=1)
        logger.info("< Step Common :: 查找并点击浮动窗口中的打开主页窗口按钮 >")
        main_window = com_get_yakushi_window(
            auto_id=mw["auto_id"], searchDepth=1, timeout=1
        )
        logger.info("< Step Common :: 再次查找主页窗口 >")
        if main_window is None:
            # The main window is still not open, so try to click the float window and show it again
            logger.info("< Step Common :: 未找到主页窗口 >")
            float_logo_button = UIA.ButtonControl(
                parent=float_window, Name="", AutomationId=flb["auto_id"]
            )
            _ = UIA.clickButton(button=float_logo_button, waitTime=1)
            logger.info("< Step Common :: 查找并点击浮动窗口中的LOGO，展开浮动窗口 >")
            _ = UIA.clickButton(button=show_main_window_button, waitTime=1)
            logger.info("< Step Common :: 再次点击浮动窗口中的打开主页窗口按钮 >")
            main_window = com_get_yakushi_window(
                auto_id=mw["auto_id"], searchDepth=1, timeout=1
            )

    logger.info(f"主页窗口-RECT：[ {main_window.BoundingRectangle} ]")

    return UIA.setWindowActive(main_window)
