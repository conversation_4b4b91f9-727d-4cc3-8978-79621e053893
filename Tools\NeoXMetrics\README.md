# NeoXMetrics

A CLI tools which writing some metrics into prometheus.

# Setup

```bash
cd testing/Tools/NeoXMetrics

# Setup python environment
pip install -r requirements.txt

# The following command is used to build an executable binary
# Pls configuration the neox_metrics.spec before running the following command
pyinstaller neox_metrics.spec

# Besides that you can use the following command to build
# The neox_metrics.spec config is the same as following command (Pls update {your_env_path} !!!)
pyinstaller --clean --onefile neox_metrics.py
```

# Usage

1. 按需配置与 neox_metrics 可执行文件同级目录下的 metrics.toml 文件；
2. 执行 neox_metrics 脚本程序，操作如下：
    ```shell
    cd /your/path/to/NeoXMetrics
    nohup ./neox_metrics > neox_metrics.log 2>&1 &
    ```
3. 在 Prometheus 的配置文件中，添加对 neox_metrics 程序暴露的 HTTP 端点的抓取配置，例如：
   ```yaml
   scrape_configs:
   - job_name: 'neox_metrics'
     static_configs:
       - targets: ['localhost:7333']
   ```
   将此配置添加到 Prometheus 的 prometheus.yml 文件中，并重启 Prometheus，Prometheus 就会开始定期抓取 http://localhost:7333/metrics 上暴露的指标数据。
