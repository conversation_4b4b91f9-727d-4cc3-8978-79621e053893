# genParam

NeoX Customization Tool, designed for segmenting PDF files, converting them into JPEG images and generating parameterized files (Format: csv).

## Dependencies

``` bash
sudo apt-get install -y python3-pip

sudo pip3 install pdf2image pillow pandas

# The pdf2image library relies on poppler-utils to process PDF files.
sudo apt-get install -y poppler-utils
```

## Usage

1. Put the PDF files in `./input/pdf/*/*.pdf`
2. Configure the parameters in `config.toml`
3. Run `python3 generate.py`
4. The output files are generated in `./output/csv/*.csv` and `./output/jpeg/*/*.jpeg`