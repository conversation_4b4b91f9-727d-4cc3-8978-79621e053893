# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-11
<AUTHOR>   <PERSON>noLu
@Email      :   <EMAIL>
@File       :   common.py
@Software   :   PyCharm
"""
import json
import toml
import yaml
from pathlib import Path
from dataclasses import dataclass, field
from typing import MutableMapping, Any, List, Dict, Mapping, Union


def load_toml(
    config_file: Path
) -> Union[MutableMapping[str, Any], List[Dict[str, Any]]]:
    """
    Load a TOML file and return the data as a Python object.

    Args:
        config_file (Path): The path to the TOML file.

    Returns:
        MutableMapping[str, Any] or List[Dict[str, Any]]: The data from the TOML file, as a Python object.
    """
    with open(config_file, 'r', encoding='utf-8') as f:
        config = toml.load(f)
    return config


def dump_toml(
        config_file: Path, object_dict: Union[Mapping[Any, Any],
                                              List[Mapping[str,
                                                           Any]]]) -> Path:
    """
    Writes a TOML file to disk.

    Args:
        config_file (Path): The path to the TOML file.
        object_dict (Mapping[Any, Any] or List[Mapping[str, Any]]): The data to write to the TOML file.

    Returns:
        Path: The path to the written TOML file.
    """
    with open(config_file, 'w', encoding='utf-8') as f:
        _ = toml.dump(object_dict, f)
    return config_file


def load_yml(config_file: Path) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
    """Load a YAML file and return the data as a Python object.

    Args:
        config_file (Path): The path to the YAML file.

    Returns:
        Dict[str, Any] or List[Dict[str, Any]]: The data from the YAML file, as a Python object.
    """
    with open(config_file, 'r', encoding='utf-8') as f:
        config = yaml.load(f.read(), Loader=yaml.FullLoader)
    return config


def dump_yml(
        config_file: Path, object_dict: Union[Mapping[Any, Any],
                                              List[Mapping[str,
                                                           Any]]]) -> Path:
    """
    Writes a YAML file to disk.

    Args:
        config_file (Path): The path to the YAML file.
        object_dict (Mapping[Any, Any] or List[Mapping[str, Any]]): The data to write to the YAML file.

    Returns:
        Path: The path to the written YAML file.
    """
    with open(config_file, 'w', encoding='utf-8') as f:
        _ = yaml.dump(object_dict,
                      f,
                      default_flow_style=False,
                      encoding='utf-8',
                      allow_unicode=True)
    return config_file


def load_json_file(
        json_file: Path) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
    """Load a JSON file and return the data as a Python object.

    Args:
        json_file (Path): The path to the JSON file.

    Returns:
        Dict[str, Any] or List[Dict[str, Any]]: The data from the JSON file, as a Python object.
    """
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data


def dump_json_file(
        json_file: Path, object_dict: Union[Mapping[Any, Any],
                                            List[Mapping[str, Any]]]) -> Path:
    """
    Writes a JSON file to disk.

    Args:
        json_file (Path): The path to the JSON file.
        object_dict (Mapping[Any, Any] or List[Mapping[str, Any]]): The data to write to the JSON file.

    Returns:
        Path: The path to the written JSON file.
    """
    with open(json_file, "w") as jf:
        _ = jf.write(json.dumps(object_dict, indent=2, ensure_ascii=False))
        # _ = jf.write(orjson.dumps(object_dict, option=orjson.OPT_INDENT_2).decode())
    return json_file


@dataclass(frozen=False)
class ProjectConfig:
    """
    This class is used to store the configuration of the NeoX project.

    Attributes:
        conf_path (list[str]): A list of the directory names and the file name of the configuration file.

    Methods:
        conf_data: Returns the configuration data from the configuration file.
    """
    conf_path: list[str] = field(default_factory=lambda: ["conf", "neox.toml"])

    def conf_data(self):
        """
        Returns the configuration data from the configuration file.

        Returns:
            dict: The configuration data from the configuration file.
        """
        return load_toml(Path.cwd().joinpath(*self.conf_path))
