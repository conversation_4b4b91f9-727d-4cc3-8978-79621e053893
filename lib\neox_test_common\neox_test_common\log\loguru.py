# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-11
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   loguru.py
@Software   :   PyCharm
"""
import logging
from loguru import logger


class PropagateHandler(logging.Handler):
    """
    Propagate logging handler to stdout console
    """

    def emit(self, record):
        logging.getLogger(record.name).handle(record)


logger.remove(handler_id=None)

logger.add(sink=PropagateHandler(),
           format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {thread} | {message}",
           backtrace=True,
           diagnose=True)
