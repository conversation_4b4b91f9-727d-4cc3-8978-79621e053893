# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-11
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   config.py
@Software   :   PyCharm
"""
from dataclasses import dataclass


@dataclass(frozen=True)
class YakushiFeaturesName:
    """
    This class is used to store the name of the Yakushi features name.

    Attributes:
        LOGIN_LOGOUT (str): The name of the login and logout features.
        HOMEPAGE_RV (str): The name of the reviewed list feature on the homepage.
        HOMEPAGE_URV (str): The name of the unreviewed list feature on the homepage.
        PH_DETAIL (str): The name of the detail on the prescription history.
        PH_FAULT (str): The name of the fault list feature on the prescription history.
        PH_FILTER (str): The name of the filter list feature on the prescription history.
        PH_PAGING (str): The name of the paging list feature on the prescription history.
        NSIPS_CLEANUP (str): The name of the source cleanup feature on the NSIPS Comparison.
        NSIPS_DETAIL (str): The name of the detail feature on the NSIPS Comparison.
        NSIPS_FILTER (str): The name of the filter feature on the NSIPS Comparison.
        NSIPS_PAGING (str): The name of the paging feature on the NSIPS Comparison.
        NSIPS_SORT (str): The name of the sort feature on the NSIPS Comparison.
        NSIPS_UPDATE (str): The name of the update feature on the NSIPS Comparison.
        FAULT_FP (str): The name of fault feedback process feature on the Other.
        SETTING_PATH (str): The name of input and output path feature on the Setting.
        SETTING_QR (str): The name of config QR feature on the Setting.
        SETTING_NOTIF (str): The name of config send pdf notification feature on the Setting.
        SETTING_INPUT (str): The name of config input file feature on the Setting.
        UI_FW (str): The name of valid floating window feature on the UI.
        UI_HPW (str): The name of valid homepage window file feature on the UI.
        UI_POPUP (str): The name of valid popup window feature on the UI.
    """
    # Authentication
    LOGIN_LOGOUT: str = "./登录登出/authentication.feature"
    # Homepage
    HOMEPAGE_RV: str = "./首页/reviewed.feature"
    HOMEPAGE_URV: str = "./首页/unreviewed.feature"
    # Prescription History
    PH_DETAIL: str = "./处方笺履历/detail.feature"
    PH_FAULT: str = "./处方笺履历/fault.feature"
    PH_FILTER: str = "./处方笺履历/filter.feature"
    PH_PAGING: str = "./处方笺履历/paging.feature"
    # NSIPS Comparison
    NSIPS_CLEANUP: str = "./NSIPS突合/cleanup.feature"
    NSIPS_DETAIL: str = "./NSIPS突合/detail.feature"
    NSIPS_FILTER: str = "./NSIPS突合/filter.feature"
    NSIPS_PAGING: str = "./NSIPS突合/paging.feature"
    NSIPS_SORT: str = "./NSIPS突合/sort.feature"
    NSIPS_UPDATE: str = "./NSIPS突合/update.feature"
    # Setting
    SETTING_PATH: str = "./设定/path.feature"
    SETTING_QR: str = "./设定/qr.feature"
    SETTING_NOTIF: str = "./设定/notification.feature"
    SETTING_INPUT: str = "./设定/input.feature"
    # UI
    UI_FW: str = "./UI/floating.feature"
    UI_HPW: str = "./UI/homepage.feature"
    UI_POPUP: str = "./UI/popup.feature"
    # Other (fault feedback process)
    FAULT_FP: str = "./其他/fault.feature"


FEATURES_NAME = YakushiFeaturesName()
