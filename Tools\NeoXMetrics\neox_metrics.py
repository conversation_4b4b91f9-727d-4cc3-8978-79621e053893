# -*- coding: utf-8 -*-
"""
@Date       :   2024/9/6
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   neox_metrics
@Software   :   PyCharm
"""
from prometheus_client import start_http_server, Gauge
from common.configuration import load_toml
from pathlib import Path
from typing import Union
import pendulum
import redis
import time


class NeoXMetrics(object):

    def __init__(self, config_filepath: Path):
        """
        初始化实例

        参数：
        config_filepath (Path): 配置文件路径
        """

        # 获取配置文件信息
        config = load_toml(config_filepath)
        # Redis 连接配置
        self.redis_host = config['base']['redis']['redis_host']
        self.redis_port = config['base']['redis']['redis_port']
        self.redis_password = config['base']['redis']['redis_password']
        # 以暴露指标的 HTTP 服务器配置
        self.http_server_port = config['base']['prometheus'][
            'http_server_port']
        self.http_server_addr = config['base']['prometheus'][
            'http_server_addr']
        # 写入数据的迭代时间（秒）
        self.interval = config['base']['interval_sec']
        # 滑动窗口大小（秒）
        self.sliding_window_sizes = [
            gauge['sliding_window_sec'] for gauge in config['metrics']['gauge']
        ]

        # 要监控的 Redis TS keys
        self.redis_ts_keys = [
            gauge['redis']['ts_keys'] for gauge in config['metrics']['gauge']
        ]

        # Prometheus 指标
        self.gauges = [
            Gauge(name=gauge['name'],
                  documentation=gauge['documentation'],
                  labelnames=gauge['label_names'])
            for gauge in config['metrics']['gauge']
        ]

    def get_last_time_series_value(self, ts_key):
        """
        获取最后一个时间序列值。

        参数：
        ts_key (string): 时间序列键名

        返回：
        float: 最后一个时间序列值
        """

        # 连接到 Redis
        r = redis.StrictRedis(host=self.redis_host,
                              port=self.redis_port,
                              password=self.redis_password,
                              decode_responses=True)

        # 使用 TS.GET 命令获取时间序列最后一个值
        last_value = r.execute_command('TS.GET', ts_key)

        if last_value is not None:
            _, value = last_value
            return value
        else:
            return None

    def calc_sliding_window_avg_value(self, ts_key, sliding_window_size: int):
        """
        计算滑动时间窗口的平均值。

        参数：
        ts_key (string): 时间序列键名

        返回：
        float: 滑动时间窗口的平均值
        """
        # 滑动时间窗口的大小（例如，过去 1 分钟的数据）
        window_size = pendulum.duration(seconds=float(sliding_window_size))
        # 获取当前时间
        now = pendulum.now('UTC')
        # 计算窗口的开始时间
        start_time = now - window_size
        # 将时间转换为 Redis 时间戳格式（毫秒）
        start_timestamp = int(start_time.timestamp() * 1000)
        end_timestamp = int(now.timestamp() * 1000)

        # 连接到 Redis
        r = redis.StrictRedis(host=self.redis_host,
                              port=self.redis_port,
                              password=self.redis_password,
                              decode_responses=True)

        # 使用 TS.RANGE 命令获取指定时间窗口内的数据
        data = r.execute_command('TS.RANGE', ts_key, start_timestamp,
                                 end_timestamp)

        # 计算平均值
        if data:
            values = [float(value) for _, value in data]
            avg_value = sum(values) / len(values)
            return avg_value
        else:
            return None

    def write_to_prometheus_metrics(self):
        """
        从 Redis 时间序列中读取数据并将其写入 Prometheus 指标
        """

        # 启动 Prometheus HTTP 服务器
        start_http_server(port=self.http_server_port,
                          addr=self.http_server_addr)

        while True:
            for idx, (gauge, redis_ts_keys) in enumerate(
                    zip(self.gauges, self.redis_ts_keys, strict=True)):
                # 滑动时间窗口的大小（秒）
                sliding_window_size = self.sliding_window_sizes[idx]
                match gauge._name:
                    case 'kenta_scheduling_status':
                        for redis_ts_key in redis_ts_keys:
                            key_value = self.get_last_time_series_value(
                                redis_ts_key[0])
                            if key_value is not None:
                                # 将值设置到 Prometheus 指标中
                                gauge.labels(
                                    status=redis_ts_key[1]).set(key_value)
                    case 'kenta_scheduling_status_transform':
                        for redis_ts_key in redis_ts_keys:
                            key_value = self.calc_sliding_window_avg_value(
                                redis_ts_key[0], sliding_window_size)
                            if key_value is not None:
                                # 将值设置到 Prometheus 指标中
                                gauge.labels(
                                    status=redis_ts_key[1]).set(key_value)
                    case 'kenta_scheduling_status_transform_custom_sec':
                        for redis_ts_key in redis_ts_keys:
                            key_value = self.calc_sliding_window_avg_value(
                                redis_ts_key[0], sliding_window_size)
                            if key_value is not None:
                                # 将值设置到 Prometheus 指标中
                                gauge.labels(
                                    status=redis_ts_key[1]).set(key_value)
                    case _:
                        pass

            # 获取一次的间隔时间
            time.sleep(self.interval)


def main(filepath: Union[Path, str]):
    """
    应用主函数。

    参数：
    filepath (Union[Path, str]): 配置文件路径
    """
    file_path = Path(filepath) if isinstance(filepath, str) else filepath
    NeoXMetrics(file_path).write_to_prometheus_metrics()


if __name__ == "__main__":
    # 当用作主程序时，以当前目录下的 metrics.toml 作为配置文件启动程序
    main(Path.cwd().joinpath('metrics.toml'))
