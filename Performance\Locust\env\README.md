# Project Setup and Workflow

This document provides instructions for setting up the project environment, building local packages, and deploying the application. This project uses [uv](https://github.com/astral-sh/uv) for package and virtual environment management.

`pyproject.toml` defines the project dependencies, and `uv.toml` contains specific configurations for `uv`, such as the Python interpreter and PyPI index URL.

## 1. Environment Setup (with uv)

### 1.1. Install uv

Before you start, you need to install `uv`. Run the command for your operating system:

**macOS & Linux:**
```shell
curl -Lfs https://astral.sh/uv/install.sh | sh
```

**Windows:**
```shell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```
For more installation options, see the [official uv documentation](https://docs.astral.sh/uv/installing/).

### 1.2. Setup Steps

Execute the following steps in the current directory (`Performance/Locust/env/`).

**Step 1: Create the Virtual Environment**

`uv` will automatically discover the `uv.toml` file in the current directory and use the specified Python interpreter to create the virtual environment.

```bash
# This will create a .venv directory in the current folder
uv venv
```

**Step 2: Activate the Virtual Environment**

**macOS & Linux:**
```bash
source .venv/bin/activate
```

**Windows:**
```powershell
.venv\Scripts\Activate.ps1
```

**Step 3: Install Dependencies**

Project dependencies are split into `dev` (for development) and `latest` (for CI/production). Choose one based on your needs.

*   **To install development dependencies:**
    This installs core dependencies, extra tools for development (like `pipdeptree`), and local packages in editable mode.
    ```bash
    # The '-e' flag stands for "editable" mode
    uv pip install -e .[dev]
    ```

*   **To install `latest` (CI) dependencies:**
    This installs core dependencies and local packages from pre-built `.whl` files, suitable for CI/CD environments.
    ```bash
    uv pip install .[latest]
    ```

After these steps, your environment is ready.

## 2. Building Wheels for Local Packages

This section outlines how to build the `.whl` files for the local libraries (`locust_common`, `neox_locust`). For more detailed instructions on different build methods, please see the [Build Guide](../../../lib/Build.md).

1.  **Build the package**: Navigate to the target library's source directory (e.g., `lib/locust_common`) and run `hatch build`. This will generate a wheel file in that library's `dist/` subdirectory.
    ```bash
    # Example for building the locust_common package
    cd ../../../lib/locust_common
    hatch build
    ```

2.  **Move the wheel file**: After the build, move the new wheel from the library's `dist/` folder to this environment's local `lib/` directory (`Performance/Locust/env/lib/`).
    ```bash
    # Example: run from the 'Performance/Locust/env' directory
    mv ../../../lib/locust_common/dist/*.whl ./lib/
    ```

3.  **Update Configuration**:
    *   Update the `./.env` file with the new version if it is tracked there.
    *   Update the package reference in this project's `pyproject.toml` under `[project.optional-dependencies.latest]` to match the new wheel file's name and version.

## 3. Deployment

### Stage 1: Build Docker Image

1.  Edit the `.env` file.
2.  Confirm all variables in `.env` are correct.
3.  Check the `Dockerfile`.
4.  Run `bash BuildImage.sh` to build the custom Locust Docker image.

### Stage 2: Run Load Test

1.  Check `docker-compose.yml` and `.env`.
2.  Run `docker-compose up -d` to start the load test.
3.  For debugging, check the logs:
    *   `${VOLUMES_PATH}/scenerios/\*/\*/run.log`
    *   `${VOLUMES_PATH}/scenerios/\*/\*/internal_*.log` 